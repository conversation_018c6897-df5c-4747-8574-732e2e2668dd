// 播放清單詳情樣式

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 縮圖懸停效果
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

// 下拉選單樣式
.dropdown-content {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid hsl(var(--bc) / 0.1);
}

// 響應式調整
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .navbar {
    padding: 0 1rem;
  }
  
  // 在小螢幕上調整網格佈局
  .grid.grid-cols-12 {
    grid-template-columns: auto 1fr auto auto;
    gap: 0.5rem;
  }
  
  .col-span-1 {
    grid-column: span 1;
  }
  
  .col-span-2 {
    display: none; // 隱藏縮圖欄位
  }
  
  .col-span-6 {
    grid-column: span 1;
  }
  
  .col-span-2:last-of-type {
    display: none; // 隱藏時間欄位
  }
  
  .col-span-1:last-child {
    grid-column: span 1;
  }
}

@media (max-width: 640px) {
  .navbar-center h1 {
    font-size: 1rem;
  }
  
  .navbar-end .btn {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
  
  .navbar-end .btn svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.25rem;
  }
}
