{"radio-online-server": {"root": "apps/radio-online-server", "sourceRoot": "apps/radio-online-server", "projectType": "application", "targets": {"build": {"executor": "@nx/js:tsc", "options": {"outputPath": "dist/apps/radio-online-server", "main": "apps/radio-online-server/index.ts", "tsConfig": "apps/radio-online-server/tsconfig.json", "assets": ["apps/radio-online-server/docker-compose.yml", "apps/radio-online-server/Dockerfile"]}}, "serve": {"executor": "@nx/workspace:run-commands", "options": {"cwd": "apps/radio-online-server", "commands": ["docker compose down || true", "mkdir -p data", "chmod -R 777 data || true", "docker compose build", "docker compose up -d", "docker logs -f $(docker ps -q --filter name=radio-online-server)"], "parallel": false}}}, "tags": []}}