{"name": "radio-online", "version": "0.0.0", "scripts": {"ng": "ng", "start": "nx serve --host 0.0.0.0", "build": "nx build", "watch": "nx build --watch --configuration development", "test": "nx test", "start-server": "node dist/apps/radio-online-server/index.js", "start:all": "concurrently \"npm run start\" \"npm run start-server\"", "build-server": "tsc -p apps/radio-online-server/tsconfig.json", "dev-server": "npm run build-server -- --watch & npm run start-server"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/cdk": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@angular/youtube-player": "^18.0.0", "@socket.io/admin-ui": "^0.5.1", "better-sqlite3": "^11.10.0", "core-js": "^2.6.12", "daisyui": "^5.0.37", "hls.js": "^1.5.20", "ngx-socket-io": "^4.8.2", "radio-browser-api": "^6.0.2", "rxjs": "~7.8.0", "tailwindcss": "^4.1.7", "tslib": "^2.3.0", "vconsole": "^3.15.1", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.1", "@angular-devkit/core": "^18.0.1", "@angular-devkit/schematics": "^18.0.1", "@angular/cli": "^18.0.1", "@angular/compiler-cli": "^18.0.1", "@nx/angular": "21.1.2", "@nx/workspace": "21.1.2", "@schematics/angular": "^18.0.1", "@tailwindcss/postcss": "^4.1.7", "@types/better-sqlite3": "^7.6.13", "@types/jasmine": "~5.1.0", "@types/node": "^22.12.0", "@types/socket.io-client": "^1.4.36", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "jasmine-core": "~5.5.0", "nodemon": "^3.1.9", "nx": "21.1.2", "postcss": "^8.5.3", "ts-node": "^10.9.2", "typescript": "~5.5.0"}, "type": "module"}