// 播放清單管理器樣式

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 卡片懸停效果
.card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
  }
}

// 模態框背景
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

// 響應式調整
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .navbar {
    padding: 0 1rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
}
