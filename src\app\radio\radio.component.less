// Sidebar 樣式
.sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;

  @media (max-width: 768px) {
    position: relative;
    height: auto;
    min-height: 40vh;
  }
}

.sidebar-header {
  // 固定標題區域，不參與滾動
  position: sticky;
  top: 0;
  z-index: 20;
}

.sidebar-content {
  // 可滾動的內容區域
  height: calc(100vh - 80px); // 減去標題區域的高度
  overflow-y: auto;
  overflow-x: hidden;

  // 自定義滾動條樣式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;

    &:hover {
      background: hsl(var(--bc) / 0.5);
    }
  }

  // Firefox 滾動條樣式
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--bc) / 0.3) hsl(var(--b2));

  @media (max-width: 768px) {
    height: auto;
    max-height: 40vh;
  }
}

// 播放器容器樣式
.player-container {
  margin-left: 0;
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  overflow: hidden;

  @media (min-width: 768px) {
    margin-left: 25%; // 為左側 sidebar 留出空間
    width: 75%;
  }

  @media (max-width: 768px) {
    position: relative;
    height: auto;
    min-height: 60vh;
    width: 100%;
  }
}

.player-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  // 自定義滾動條樣式（與左側一致）
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;

    &:hover {
      background: hsl(var(--bc) / 0.5);
    }
  }

  // Firefox 滾動條樣式
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--bc) / 0.3) hsl(var(--b2));

  @media (max-width: 768px) {
    height: auto;
    overflow-y: visible;
  }
}

// 電台項目樣式
.station {
  transition: all 0.2s ease;

  &.selected {
    background: hsl(var(--p) / 0.1);
    border-color: hsl(var(--p));

    strong {
      color: hsl(var(--p));
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px hsl(var(--bc) / 0.1);
  }
}

.station-tags {
  width: 100%;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
}

// 原有樣式保留
.radio-container {
  padding: 20px;
}

.stations {
  display: grid;
  gap: 15px;
}

button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}

:host ::ng-deep {
  .dark-theme-card {
    .p-card {
      background: var(--surface-900);
      color: white;
    }

    .p-card-header, .p-card-content, .p-card-footer {
      color: white;
    }
  }
}