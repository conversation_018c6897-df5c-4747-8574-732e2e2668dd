<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>芯禾電台 電視TV專用版</title>
    <!-- Cache Control -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css?ver2025060502" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <div class="container-fluid">
        <div class="row flex-column flex-md-row vh-100">
            <!-- 左側選單 -->
            <div class="col-md-3 sidebar order-1 order-md-1">
                <div class="d-flex justify-content-between align-items-center p-3 bg-dark text-white">
                    <div class="d-flex align-items-center">
                        <h2 class="mb-0">芯禾電台TV</h2>
                        <span class="badge bg-success ms-2" id="onlineUsers">線上人數: 0</span>
                    </div>
                    <button class="btn btn-outline-light" id="themeToggle">
                        <i class="bi bi-moon"></i>
                    </button>
                </div>

                <div id="stationList" class="list-group list-group-flush">
                    <!-- 電台列表將由 JavaScript 動態生成 -->
                </div>
            </div>

            <!-- 右側內容區 -->
            <div class="col-md-9 bg-white order-2 order-md-2">
                <div id="playerContainer" class="p-4">
                    <!-- 音頻播放器 -->
                    <audio id="audioPlayer" crossorigin="anonymous"></audio>

                    <!-- 播放控制區 -->
                    <div class="card" id="controlCard">
                        <div class="card-header">
                            <h3 id="currentStationName" class="text-center">未選擇電台</h3>
                        </div>
                        <div class="card-body">
                            <!-- 音量控制 -->
                            <div class="volume-control d-flex align-items-center gap-2">
                                <i class="bi bi-volume-down"></i>
                                <input type="range" class="form-range" id="volumeSlider" min="0" max="100" value="100">
                                <i class="bi bi-volume-up"></i>
                            </div>
                        </div>
                    </div>

                    <!-- YouTube 播放器區域 -->
                    <div id="youtubeSection" style="display: none;">
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="youtube-container">
                                    <div id="youtubePlayer"></div>
                                </div>
                                <!-- 播放控制按鈕 - 移到這裡 -->
                                <div class="playback-controls d-flex justify-content-center gap-3 my-3">
                                    <button class="btn btn-outline-primary" id="prevButton" disabled>
                                        <i class="bi bi-skip-backward-fill"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" id="nextButton" disabled>
                                        <i class="bi bi-skip-forward-fill"></i>
                                    </button>
                                </div>
                                <div class="playlist-controls">
                                    <textarea class="form-control mb-2" id="youtubeUrlInput" rows="3" 
                                            placeholder="請輸入 YouTube 網址（每行一個）"></textarea>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" id="addToPlaylist">
                                            <i class="bi bi-plus-lg"></i> 加入播放清單
                                        </button>
                                        <button class="btn btn-danger" id="clearPlaylist">
                                            <i class="bi bi-trash"></i> 清除播放清單
                                        </button>
                                    </div>
                                </div>
                                <div id="playlistContainer" class="mt-3">
                                    <!-- 播放清單將由 JavaScript 動態生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@1.5.20"></script>
    <script src="app.js?ver2025061004"></script>
</body>
</html>
